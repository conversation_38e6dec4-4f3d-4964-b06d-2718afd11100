#!/usr/bin/env python3
"""
ORA-P1 Web Interface Launcher
============================

Quick launcher for the Hume EVI web interface.
This script starts the web server and opens the browser automatically.

Usage:
    python start_web_interface.py
"""

import subprocess
import webbrowser
import time
import sys
import os


def main():
    """Launch the web interface."""
    print("🎭 ORA-P1 - Hume EVI Web Interface Launcher")
    print("=" * 50)
    
    # Check if required files exist
    required_files = ['index.html', 'evi-client.js', 'server.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        print("Please make sure all web interface files are present.")
        sys.exit(1)
    
    print("✅ All required files found")
    print("🚀 Starting web server...")
    
    try:
        # Start the server in the background
        server_process = subprocess.Popen([
            sys.executable, 'server.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a moment for server to start
        time.sleep(2)
        
        # Check if server started successfully
        if server_process.poll() is None:
            print("✅ Web server started successfully")
            print("🌐 Opening browser...")
            
            # Open browser
            webbrowser.open('http://localhost:8000')
            
            print("\n📋 Instructions:")
            print("1. Enter your Hume API credentials in the web interface")
            print("2. Click 'Connect to EVI' to start the conversation")
            print("3. Allow microphone access when prompted")
            print("4. Start speaking to test the voice conversation")
            print("\n⏹️  Press Ctrl+C to stop the server")
            
            # Wait for the server process
            server_process.wait()
            
        else:
            stdout, stderr = server_process.communicate()
            print(f"❌ Server failed to start:")
            if stderr:
                print(stderr.decode())
            if stdout:
                print(stdout.decode())
                
    except KeyboardInterrupt:
        print("\n👋 Shutting down...")
        if 'server_process' in locals():
            server_process.terminate()
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
