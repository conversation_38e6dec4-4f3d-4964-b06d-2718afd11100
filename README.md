# ORA-P1 - Hume EVI Integration

**Empathic Voice Interface Integration for Therapeutic AI**

This project integrates the Hume Empathic Voice Interface (EVI) API to enable real-time speech-to-speech conversations with emotional intelligence on localhost. This is the first engineering milestone for the ORA-P1 therapeutic AI system.

## 🎯 Project Overview

The Hume EVI integration provides:
- **Real-time voice conversations** using WebSocket connections
- **Emotional analysis** with prosody-based emotion detection
- **Speech-to-Speech (S2S)** interaction with <1s latency
- **Custom configuration support** for therapeutic personas and voices
- **Localhost deployment** for development and testing

## 🚀 Quick Start

### Prerequisites

- **Python 3.9-3.11** (macOS/Linux supported)
- **Hume API Account** with API keys
- **Audio system dependencies** (ffmpeg, etc.)

### 1. <PERSON><PERSON> and Setup

```bash
git clone <your-repo-url>
cd ORA-P1

# Create virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # On macOS/Linux

# Install dependencies
pip install -r requirements.txt
```

### 2. Configure API Credentials

1. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

2. Get your Hume API credentials:
   - Visit [Hume Platform API Keys](https://platform.hume.ai/settings/keys)
   - Copy your API key

3. Create an EVI configuration:
   - Visit [EVI Configurations](https://platform.hume.ai/evi/configurations)
   - Create a new configuration for your therapeutic AI persona
   - Copy the configuration ID

4. Update your `.env` file:
   ```env
   HUME_API_KEY="your_actual_api_key_here"
   HUME_SECRET_KEY="your_secret_key_here"
   HUME_CONFIG_ID="your_config_id_here"
   ```

### 3. Install System Dependencies

#### macOS
```bash
# Install ffmpeg using Homebrew
brew install ffmpeg
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt-get update
sudo apt-get install libasound2-dev libportaudio2 ffmpeg
```

### 4. Run the Integration

#### Option 1: Quickstart (Minimal)
```bash
python quickstart.py
```

#### Option 2: Full Integration (Advanced)
```bash
python hume_evi_integration.py
```

## 📁 Project Structure

```
ORA-P1/
├── README.md                    # This file
├── requirements.txt             # Python dependencies
├── .env.example                 # Environment template
├── .env                        # Your API credentials (gitignored)
├── .gitignore                  # Git ignore rules
├── quickstart.py               # Minimal EVI integration
└── hume_evi_integration.py     # Full-featured integration
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `HUME_API_KEY` | Your Hume API key | ✅ Yes |
| `HUME_SECRET_KEY` | Your Hume secret key | ⚠️ Optional |
| `HUME_CONFIG_ID` | EVI configuration ID | ✅ Yes |

### EVI Configuration

Create your EVI configuration at [platform.hume.ai](https://platform.hume.ai/evi/configurations) with:

- **Persona**: Therapeutic AI assistant for CBT
- **Voice**: Choose appropriate voice characteristics
- **Behavior**: Configure interruption handling, response style
- **Tools**: Add any custom functions if needed

## 🎭 Features

### Emotional Intelligence
- Real-time emotion detection from speech prosody
- Display of top 3 emotions with confidence scores
- Emotional context for therapeutic conversations

### Audio Processing
- High-quality audio input/output
- Low-latency streaming (<1s typical)
- Automatic audio format handling

### WebSocket Communication
- Persistent connection to Hume EVI
- Real-time message handling
- Error recovery and reconnection

## 🛠️ Usage Examples

### Basic Conversation
```python
# Start a conversation
python quickstart.py

# The system will:
# 1. Connect to Hume EVI
# 2. Start listening to your microphone
# 3. Process speech and emotions
# 4. Respond with voice and text
# 5. Display emotional analysis
```

### Advanced Features
```python
# Use the full integration for:
# - Enhanced logging
# - Better error handling
# - Customizable emotion display
# - Extended configuration options

python hume_evi_integration.py
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Missing API Credentials
```
❌ Error: HUME_API_KEY not set in .env file
```
**Solution**: Update your `.env` file with valid credentials from [Hume Platform](https://platform.hume.ai/settings/keys)

#### 2. Audio System Issues
```
❌ Error: Could not initialize audio system
```
**Solution**:
- macOS: `brew install ffmpeg`
- Linux: `sudo apt-get install libasound2-dev libportaudio2 ffmpeg`

#### 3. WebSocket Connection Failed
```
❌ Error: WebSocket connection failed
```
**Solution**:
- Check your internet connection
- Verify API credentials are correct
- Ensure configuration ID exists

#### 4. Python Version Issues
```
❌ Error: Unsupported Python version
```
**Solution**: Use Python 3.9-3.11 (3.12+ not yet supported for EVI)

### Debug Mode

Enable verbose logging by setting environment variable:
```bash
export HUME_DEBUG=1
python quickstart.py
```

## 📚 API Reference

### Key Classes

#### `HumeEVIIntegration`
Main integration class with methods:
- `start_conversation()`: Begin EVI session
- `extract_top_emotions()`: Process emotion data
- `on_message()`: Handle WebSocket messages

#### Message Types
- `chat_metadata`: Session information
- `user_message`: Your speech input
- `assistant_message`: AI response
- `audio_output`: Voice response data
- `error`: Error messages

## 🔗 Resources

- [Hume EVI Documentation](https://dev.hume.ai/docs/empathic-voice-interface-evi/overview)
- [Python SDK Documentation](https://github.com/HumeAI/hume-python-sdk)
- [EVI Configuration Guide](https://dev.hume.ai/docs/empathic-voice-interface-evi/configuration/build-a-configuration)
- [API Reference](https://dev.hume.ai/reference/empathic-voice-interface-evi/chat/chat)

## 🤝 Contributing

This is the first engineering milestone for ORA-P1. Future enhancements will include:
- Web interface integration
- Advanced therapeutic conversation flows
- Multi-modal emotion detection
- Session management and history
- Integration with CBT frameworks

## 📄 License

[Add your license information here]

---

**Next Steps**: Once this integration is working, we'll build the web interface and integrate with the therapeutic AI framework.
