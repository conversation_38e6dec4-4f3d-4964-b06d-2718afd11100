#!/usr/bin/env python3
"""
Hume EVI Quickstart - Minimal Implementation
===========================================

A minimal implementation of <PERSON>'s Empathic Voice Interface (EVI) 
based on the official quickstart example.

This script demonstrates:
- Authentication with Hume API
- WebSocket connection to EVI
- Real-time audio input/output
- Emotion detection and display

Usage:
    python quickstart.py

Make sure to set your API credentials in the .env file first.
"""

import asyncio
import base64
import datetime
import os
from dotenv import load_dotenv
from hume import MicrophoneInterface, Stream
from hume.client import AsyncHumeClient
from hume.empathic_voice.chat.socket_client import ChatConnectOptions
from hume.empathic_voice.chat.types import SubscribeEvent


def extract_top_n_emotions(emotion_scores: dict, n: int) -> dict:
    """Extract top N emotions from emotion scores."""
    sorted_emotions = sorted(emotion_scores.items(), key=lambda item: item[1], reverse=True)
    top_n_emotions = {emotion: score for emotion, score in sorted_emotions[:n]}
    return top_n_emotions


def print_emotions(emotion_scores: dict) -> None:
    """Print emotions in a formatted way."""
    print(
        ' | '.join([f"{emotion} ({score:.2f})" for emotion, score in emotion_scores.items()])
    )


def log(text: str) -> None:
    """Log a message with timestamp."""
    now = datetime.datetime.now(tz=datetime.timezone.utc).strftime("%H:%M:%S")
    print(f"[{now}] {text}")


async def main() -> None:
    """Main function to run the EVI quickstart."""
    # Load environment variables
    load_dotenv()

    HUME_API_KEY = os.getenv("HUME_API_KEY")
    HUME_CONFIG_ID = os.getenv("HUME_CONFIG_ID")

    # Validate environment variables
    if not HUME_API_KEY or HUME_API_KEY == "PASTE_YOUR_API_KEY_HERE":
        print("❌ Error: HUME_API_KEY not set in .env file")
        print("Please update your .env file with a valid API key from:")
        print("https://platform.hume.ai/settings/keys")
        return

    if not HUME_CONFIG_ID or HUME_CONFIG_ID == "PASTE_YOUR_CONFIG_ID_HERE":
        print("❌ Error: HUME_CONFIG_ID not set in .env file")
        print("Please create a configuration and update your .env file:")
        print("https://platform.hume.ai/evi/configurations")
        return

    # Initialize Hume client and audio stream
    client = AsyncHumeClient(api_key=HUME_API_KEY)
    stream = Stream.new()

    async def on_message(message: SubscribeEvent):
        """Handle incoming WebSocket messages."""
        if message.type == "chat_metadata":
            log(
                f"<{message.type}> Chat ID: {message.chat_id}, Chat Group ID: {message.chat_group_id}"
            )
        elif message.type == "user_message" or message.type == "assistant_message":
            log(f"{message.message.role}: {message.message.content}")
            # Display emotions if available
            if hasattr(message, 'models') and message.models and message.models.prosody:
                emotions = extract_top_n_emotions(dict(message.models.prosody.scores), 3)
                if emotions:
                    print_emotions(emotions)
        elif message.type == "audio_output":
            # Stream audio output for playback
            await stream.put(
                base64.b64decode(message.data.encode("utf-8"))
            )
        elif message.type == "error":
            raise RuntimeError(
                f"Received error message from Hume websocket ({message.code}): {message.message}"
            )
        else:
            log(f"<{message.type}>")

    print("🎭 Hume EVI Quickstart")
    print("=" * 40)
    print("🚀 Starting conversation...")
    print("🎤 Speak into your microphone")
    print("⏹️  Press Ctrl+C to stop")
    print()

    try:
        # Connect to Hume EVI WebSocket
        async with client.empathic_voice.chat.connect_with_callbacks(
            options=ChatConnectOptions(config_id=HUME_CONFIG_ID),
            on_open=lambda: print("✅ WebSocket connection opened."),
            on_message=on_message,
            on_close=lambda: print("🔌 WebSocket connection closed."),
            on_error=lambda err: print(f"❌ Error: {err}")
        ) as socket:
            # Start microphone interface
            await asyncio.create_task(
                MicrophoneInterface.start(
                    socket,
                    allow_user_interrupt=False,
                    byte_stream=stream
                )
            )
    except KeyboardInterrupt:
        print("\n👋 Conversation ended by user")
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
