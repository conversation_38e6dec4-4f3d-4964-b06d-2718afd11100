#!/usr/bin/env python3
"""
Hume EVI (Empathic Voice Interface) Integration for ORA-P1
=========================================================

This script integrates the Hume Empathic Voice Interface API to enable
speech-to-speech conversations with emotional intelligence on localhost.

Features:
- Real-time voice conversation using WebSocket
- Emotional analysis and display of top emotions
- Audio input/output handling
- Configurable EVI personality and voice settings

Requirements:
- Python 3.9-3.11 (macOS/Linux)
- Hume API key and configuration ID
- Audio system dependencies (ffmpeg, etc.)

Usage:
    python hume_evi_integration.py

Author: ORA-P1 Team
Date: August 2025
"""

import asyncio
import base64
import datetime
import os
import sys
from typing import Dict, Optional

from dotenv import load_dotenv
from hume import MicrophoneInterface, Stream
from hume.client import AsyncHumeClient
from hume.empathic_voice.chat.socket_client import ChatConnectOptions
from hume.empathic_voice.chat.types import SubscribeEvent


class HumeEVIIntegration:
    """Main class for Hume EVI integration."""
    
    def __init__(self):
        """Initialize the EVI integration."""
        load_dotenv()
        
        # Load environment variables
        self.api_key = os.getenv("HUME_API_KEY")
        self.secret_key = os.getenv("HUME_SECRET_KEY")
        self.config_id = os.getenv("HUME_CONFIG_ID")
        
        # Validate required environment variables
        self._validate_environment()
        
        # Initialize Hume client and audio stream
        self.client = AsyncHumeClient(api_key=self.api_key)
        self.stream = Stream.new()
        
        # Configuration
        self.top_emotions_count = 3
        self.allow_user_interrupt = False
        
    def _validate_environment(self) -> None:
        """Validate that all required environment variables are set."""
        missing_vars = []
        
        if not self.api_key or self.api_key == "PASTE_YOUR_API_KEY_HERE":
            missing_vars.append("HUME_API_KEY")
        if not self.config_id or self.config_id == "PASTE_YOUR_CONFIG_ID_HERE":
            missing_vars.append("HUME_CONFIG_ID")
            
        if missing_vars:
            self.log_error(f"Missing required environment variables: {', '.join(missing_vars)}")
            self.log_error("Please update your .env file with valid Hume API credentials.")
            self.log_error("Get your API keys from: https://platform.hume.ai/settings/keys")
            self.log_error("Create a configuration at: https://platform.hume.ai/evi/configurations")
            sys.exit(1)
    
    def extract_top_emotions(self, emotion_scores: Dict[str, float], n: int = 3) -> Dict[str, float]:
        """
        Extract the top N emotions from emotion scores.
        
        Args:
            emotion_scores: Dictionary of emotion names to scores
            n: Number of top emotions to return
            
        Returns:
            Dictionary of top N emotions with their scores
        """
        if not emotion_scores:
            return {}
            
        sorted_emotions = sorted(
            emotion_scores.items(), 
            key=lambda item: item[1], 
            reverse=True
        )
        return {emotion: score for emotion, score in sorted_emotions[:n]}
    
    def print_emotions(self, emotion_scores: Dict[str, float]) -> None:
        """
        Print emotions in a formatted way.
        
        Args:
            emotion_scores: Dictionary of emotion names to scores
        """
        if not emotion_scores:
            return
            
        emotion_str = ' | '.join([
            f"{emotion} ({score:.2f})" 
            for emotion, score in emotion_scores.items()
        ])
        print(f"    🎭 Emotions: {emotion_str}")
    
    def log(self, text: str, level: str = "INFO") -> None:
        """
        Log a message with timestamp.
        
        Args:
            text: Message to log
            level: Log level (INFO, ERROR, WARNING)
        """
        now = datetime.datetime.now(tz=datetime.timezone.utc).strftime("%H:%M:%S")
        prefix = "🤖" if level == "INFO" else "❌" if level == "ERROR" else "⚠️"
        print(f"[{now}] {prefix} {text}")
    
    def log_error(self, text: str) -> None:
        """Log an error message."""
        self.log(text, "ERROR")
    
    def log_warning(self, text: str) -> None:
        """Log a warning message."""
        self.log(text, "WARNING")
    
    async def on_message(self, message: SubscribeEvent) -> None:
        """
        Handle incoming WebSocket messages from Hume EVI.
        
        Args:
            message: The received message event
        """
        try:
            if message.type == "chat_metadata":
                self.log(f"Chat started - ID: {message.chat_id}, Group: {message.chat_group_id}")
                
            elif message.type == "user_message":
                print(f"\n👤 You: {message.message.content}")
                # Extract and display emotions from user's speech
                if hasattr(message, 'models') and message.models and message.models.prosody:
                    emotions = self.extract_top_emotions(
                        dict(message.models.prosody.scores), 
                        self.top_emotions_count
                    )
                    self.print_emotions(emotions)
                    
            elif message.type == "assistant_message":
                print(f"\n🤖 Assistant: {message.message.content}")
                # Extract and display emotions from assistant's response
                if hasattr(message, 'models') and message.models and message.models.prosody:
                    emotions = self.extract_top_emotions(
                        dict(message.models.prosody.scores), 
                        self.top_emotions_count
                    )
                    self.print_emotions(emotions)
                    
            elif message.type == "audio_output":
                # Stream audio output for playback
                await self.stream.put(
                    base64.b64decode(message.data.encode("utf-8"))
                )
                
            elif message.type == "error":
                self.log_error(f"WebSocket error ({message.code}): {message.message}")
                raise RuntimeError(f"EVI WebSocket error: {message.message}")
                
            else:
                # Log other message types for debugging
                self.log(f"Received message type: {message.type}")
                
        except Exception as e:
            self.log_error(f"Error processing message: {str(e)}")
            raise
    
    async def start_conversation(self) -> None:
        """Start the EVI conversation."""
        self.log("🚀 Starting Hume EVI Integration...")
        self.log(f"📋 Using configuration ID: {self.config_id}")
        self.log("🎤 Speak into your microphone to begin conversation")
        self.log("⏹️  Press Ctrl+C to stop")
        
        try:
            # Connect to Hume EVI WebSocket with callbacks
            async with self.client.empathic_voice.chat.connect_with_callbacks(
                options=ChatConnectOptions(config_id=self.config_id),
                on_open=lambda: self.log("✅ WebSocket connection opened"),
                on_message=self.on_message,
                on_close=lambda: self.log("🔌 WebSocket connection closed"),
                on_error=lambda err: self.log_error(f"WebSocket error: {err}")
            ) as socket:
                
                # Start microphone interface for audio input/output
                await asyncio.create_task(
                    MicrophoneInterface.start(
                        socket,
                        allow_user_interrupt=self.allow_user_interrupt,
                        byte_stream=self.stream
                    )
                )
                
        except KeyboardInterrupt:
            self.log("👋 Conversation ended by user")
        except Exception as e:
            self.log_error(f"Conversation error: {str(e)}")
            raise
    
    async def run(self) -> None:
        """Run the EVI integration."""
        try:
            await self.start_conversation()
        except Exception as e:
            self.log_error(f"Failed to start EVI integration: {str(e)}")
            sys.exit(1)


def main() -> None:
    """Main entry point."""
    print("=" * 60)
    print("🎭 Hume EVI Integration for ORA-P1")
    print("   Empathic Voice Interface with Emotional Intelligence")
    print("=" * 60)
    
    # Create and run the EVI integration
    evi = HumeEVIIntegration()
    
    try:
        asyncio.run(evi.run())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Fatal error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
