#!/usr/bin/env python3
"""
Simple HTTP Server for ORA-P1 Web Interface
==========================================

A lightweight HTTP server to serve the Hume EVI web interface
with proper CORS headers for local development.

Usage:
    python server.py

The server will start on http://localhost:8000
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse


class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """HTTP request handler with CORS support."""
    
    def end_headers(self):
        """Add CORS headers to all responses."""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()
    
    def do_OPTIONS(self):
        """Handle preflight OPTIONS requests."""
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        """Custom log format with timestamps."""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {format % args}")


def start_server(port=8000):
    """Start the HTTP server."""
    
    # Change to the directory containing the web files
    web_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(web_dir)
    
    # Create server
    handler = CORSHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("", port), handler) as httpd:
            print("=" * 60)
            print("🚀 ORA-P1 Web Server Starting")
            print("=" * 60)
            print(f"📂 Serving files from: {web_dir}")
            print(f"🌐 Server running at: http://localhost:{port}")
            print(f"🎭 Open in browser: http://localhost:{port}")
            print("⏹️  Press Ctrl+C to stop the server")
            print("=" * 60)
            
            # List available files
            files = [f for f in os.listdir('.') if f.endswith(('.html', '.js', '.css'))]
            if files:
                print("📄 Available files:")
                for file in sorted(files):
                    print(f"   • {file}")
                print()
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Port {port} is already in use")
            print(f"💡 Try a different port: python server.py {port + 1}")
        else:
            print(f"❌ Server error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


def main():
    """Main entry point."""
    port = 8000
    
    # Allow custom port from command line
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("❌ Invalid port number. Using default port 8000.")
            port = 8000
    
    start_server(port)


if __name__ == "__main__":
    main()
